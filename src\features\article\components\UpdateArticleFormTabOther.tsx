import classNames from 'classnames';
import FileUpload from 'components/partials/FileUpload';
import PaginationTable from 'components/partials/PaginationTable';
import { ARTICLE_TAB, LIMIT_MAX, OPERATION_NAME, PAGE_NUMBER_DEFAULT, PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { X } from 'react-feather';
import { ARTICLE_LIST } from 'services/ArticleService';
import Article, { SearchArticleParam, ArticleQueryRes, SearchArticle, articleFilterConfig, NewArticleType } from 'types/Article';
import { generateFilters } from 'utils/common';

interface UpdateArticleFormTabOtherProps {
    onChangeFileArticle: (file: File) => void;
    setTabAction(tab: ARTICLE_TAB | null): void;
}

export const UpdateArticleFormTabOther = ({ onChangeFileArticle, setTabAction }: UpdateArticleFormTabOtherProps) => {
    const { queryParams, setQueryParams } = useQueryParams<SearchArticleParam>();
    const [title, setTitle] = useState('');
    const [duplicateArticles, setDuplicateArticles] = useState<Article[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const pageSize = 5;

    useEffect(() => {
        setCurrentPage(Number(queryParams.page));
    }, [queryParams.page]);

    const paramConfig: SearchArticleParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            title: queryParams.title,
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, articleFilterConfig);

    const { data } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, paramConfig, filters],
        ARTICLE_LIST,
        {
            page: Number(page),
            limit: pageSize,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!dataParamConfig.title,
        }
    );

    useEffect(() => {
        if (data) {
            const allArticles = data?.articles_list.data ?? [];
            setDuplicateArticles(allArticles);
        }
    }, [data]);

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const onCheckDuplicate = async () => {
        if(!title) {
            setDuplicateArticles([]);
            return;
        }
        setQueryParams({ ...queryParams, page: '1', title });
    };

    return (
        <div className="card !mb-0">
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Khác</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="card-body p-[.75rem]">
                    <div className="mt-1 mb-1">
                        <label className="form-label">Lấy tin bài tự động</label>
                        <input className="form-control" type="text" placeholder="Nhập link URL bài viết" />
                    </div>
                    <div className="mb-1 d-flex justify-content-end">
                        <button
                            type="button"
                            className="btn btn-sm waves-effect waves-float waves-light !bg-[#FCF3F5FF] rounded-[4px] !text-[#A42D49FF]"
                        >
                            Lấy tin
                        </button>
                    </div>
                    <div className="mb-1">
                        <div className="d-flex justify-content-between flex-md-row flex-column">
                            <label className="form-label leading-[22px]">Khôi phục bài viết</label>
                            <button
                                type="button"
                                className="btn btn-sm btn-primary waves-effect waves-float waves-light"
                            >
                                Khôi phục
                            </button>
                        </div>
                    </div>
                    <div className="mb-1">
                        <label className="form-label">Lọc trùng bài viết</label>
                        <input
                            className="form-control"
                            type="text"
                            placeholder="Nhập tiêu đề bài viết"
                            onChange={(e) => setTitle(e.target.value)}
                        />
                    </div>
                    <div className="mb-1 d-flex justify-content-end">
                        <button
                            type="button"
                            className="btn btn-sm waves-effect waves-float waves-light !bg-[#FCF3F5FF] rounded-[4px] !text-[#A42D49FF]"
                            onClick={onCheckDuplicate}
                        >
                            Kiểm tra
                        </button>
                    </div>
                    {duplicateArticles.length > 0 && (
                        <>
                            <div className="w-full rounded-md border bg-white shadow-sm mt-1">
                                <div className="px-1 py-1 border-b font-semibold">Bài viết</div>

                                <div className="divide-y">
                                    {duplicateArticles.map((article) => (
                                        <div key={article.id} className="flex gap-2 px-1 py-1 hover:bg-gray-50">
                                            <div className="flex-1">
                                                <div className="font-medium text-sm">{article.title}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <PaginationTable
                                countItem={data?.articles_list.totalCount}
                                totalPage={data?.articles_list.totalPages}
                                currentPage={currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </>
                    )}
                    <div className="mb-1">
                        <label className="form-label">Import file bài viết</label>
                        <FileUpload type="document" onFileChange={onChangeFileArticle} />
                    </div>
                </div>
            </div>
        </div>
    );
};
